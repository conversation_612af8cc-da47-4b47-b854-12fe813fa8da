<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.2" width="139.7mm" height="139.7mm" viewBox="0 0 13970 13970" preserveAspectRatio="xMidYMid" fill-rule="evenodd" stroke-width="28.222" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" xmlns:ooo="http://xml.openoffice.org/svg/export" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:presentation="http://sun.com/xmlns/staroffice/presentation" xmlns:smil="http://www.w3.org/2001/SMIL20/" xmlns:anim="urn:oasis:names:tc:opendocument:xmlns:animation:1.0" xml:space="preserve">
 <defs class="ClipPathGroup">
  <clipPath id="presentation_clip_path" clipPathUnits="userSpaceOnUse">
   <rect x="0" y="0" width="13970" height="13970"/>
  </clipPath>
 </defs>
 <defs>
  <font id="EmbeddedFont_1" horiz-adv-x="2048">
   <font-face font-family="Verdana embedded" units-per-em="2048" font-weight="bold" font-style="normal" ascent="2037" descent="423"/>
   <missing-glyph horiz-adv-x="2048" d="M 0,0 L 2047,0 2047,2047 0,2047 0,0 Z"/>
   <glyph unicode="x" horiz-adv-x="1350" d="M 1344,0 L 924,0 684,336 438,0 26,0 469,561 33,1123 453,1123 689,793 926,1123 1339,1123 902,570 1344,0 Z"/>
   <glyph unicode="w" horiz-adv-x="1933" d="M 1961,1123 L 1613,0 1231,0 1002,757 777,0 390,0 45,1123 423,1123 615,349 855,1123 1174,1123 1402,349 1591,1123 1961,1123 Z"/>
   <glyph unicode="v" horiz-adv-x="1271" d="M 1301,1123 L 868,0 460,0 30,1123 411,1123 671,351 928,1123 1301,1123 Z"/>
   <glyph unicode="u" horiz-adv-x="1138" d="M 1289,0 L 929,0 929,124 C 862,73 801,35 746,9 691,-18 625,-31 548,-31 424,-31 329,5 262,76 195,147 161,253 161,392 L 161,1123 523,1123 523,566 C 523,509 525,462 529,425 532,387 541,356 554,331 567,306 586,288 612,277 637,266 673,260 719,260 750,260 784,266 821,277 858,288 894,305 929,327 L 929,1123 1289,1123 1289,0 Z"/>
   <glyph unicode="t" horiz-adv-x="875" d="M 888,10 C 849,0 808,-8 765,-14 722,-19 669,-22 606,-22 466,-22 362,6 294,63 225,120 191,217 191,354 L 191,879 43,879 43,1123 191,1123 191,1444 551,1444 551,1123 888,1123 888,879 551,879 551,481 C 551,442 551,407 552,378 553,349 558,322 568,299 577,276 594,257 618,244 641,230 676,223 721,223 740,223 764,227 795,235 825,243 846,250 858,257 L 888,257 888,10 Z"/>
   <glyph unicode="s" horiz-adv-x="1086" d="M 1146,356 C 1146,240 1094,147 990,76 886,5 744,-31 563,-31 463,-31 372,-22 291,-3 210,16 144,36 95,59 L 95,355 128,355 C 146,342 167,329 191,314 214,299 248,282 291,265 328,250 371,237 418,226 465,215 516,209 570,209 641,209 693,217 727,233 761,248 778,271 778,302 778,329 768,349 748,362 728,374 690,386 634,397 607,403 571,410 526,417 480,424 438,432 401,442 299,469 223,510 174,567 125,623 100,693 100,777 100,885 151,975 254,1047 356,1118 496,1154 673,1154 757,1154 838,1146 915,1129 992,1112 1052,1093 1095,1074 L 1095,790 1064,790 C 1011,827 950,857 882,880 813,903 744,914 674,914 616,914 567,906 528,890 488,874 468,851 468,822 468,795 477,775 495,761 513,747 555,733 622,719 659,712 698,705 741,698 783,691 825,682 868,671 963,646 1033,608 1078,555 1123,502 1146,435 1146,356 Z"/>
   <glyph unicode="p" horiz-adv-x="1191" d="M 1342,577 C 1342,484 1328,400 1300,324 1271,248 1234,185 1188,136 1140,84 1086,45 1025,18 964,-9 899,-22 830,-22 766,-22 712,-15 667,-2 622,12 577,31 530,56 L 530,-412 170,-412 170,1123 530,1123 530,1006 C 585,1050 642,1086 700,1113 757,1140 824,1154 899,1154 1039,1154 1148,1103 1226,1000 1303,897 1342,756 1342,577 Z M 971,570 C 971,679 952,759 915,808 878,857 819,882 738,882 704,882 669,877 633,867 597,856 563,841 530,822 L 530,257 C 554,248 580,242 607,239 634,236 661,235 688,235 783,235 854,263 901,319 948,374 971,458 971,570 Z"/>
   <glyph unicode="o" horiz-adv-x="1245" d="M 1318,561 C 1318,376 1264,231 1157,125 1049,18 898,-35 703,-35 508,-35 357,18 250,125 142,231 88,376 88,561 88,747 142,893 251,999 359,1105 510,1158 703,1158 899,1158 1051,1105 1158,998 1265,891 1318,746 1318,561 Z M 875,288 C 898,317 916,351 928,392 939,432 945,488 945,559 945,625 939,680 927,725 915,770 898,805 877,832 856,859 830,879 800,890 770,901 738,907 703,907 668,907 638,902 611,893 584,884 558,865 533,837 511,811 494,775 481,730 468,685 461,628 461,559 461,498 467,445 478,400 489,355 506,318 528,291 549,265 575,246 605,234 634,222 668,216 706,216 739,216 770,222 800,233 829,244 854,262 875,288 Z"/>
   <glyph unicode="n" horiz-adv-x="1138" d="M 1298,0 L 936,0 936,557 C 936,602 934,648 929,693 924,738 916,771 905,792 892,817 872,835 847,846 821,857 785,863 739,863 706,863 673,858 640,847 606,836 569,819 530,796 L 530,0 170,0 170,1123 530,1123 530,999 C 594,1049 656,1087 715,1114 774,1141 839,1154 911,1154 1032,1154 1127,1119 1196,1048 1264,977 1298,872 1298,731 L 1298,0 Z"/>
   <glyph unicode="m" horiz-adv-x="1853" d="M 1268,796 L 1268,0 906,0 906,561 C 906,616 905,662 902,701 899,739 892,770 880,794 868,818 850,836 826,847 801,858 767,863 723,863 688,863 653,856 620,842 587,827 557,812 530,796 L 530,0 170,0 170,1123 530,1123 530,999 C 592,1048 651,1086 708,1113 765,1140 827,1154 896,1154 970,1154 1035,1138 1092,1105 1149,1072 1193,1023 1225,959 1297,1020 1367,1067 1435,1102 1503,1137 1570,1154 1635,1154 1756,1154 1849,1118 1912,1045 1975,972 2006,868 2006,731 L 2006,0 1644,0 1644,561 C 1644,616 1643,663 1641,701 1638,739 1631,770 1619,794 1608,818 1590,836 1565,847 1540,858 1506,863 1461,863 1431,863 1402,858 1373,848 1344,837 1309,820 1268,796 Z"/>
   <glyph unicode="l" horiz-adv-x="371" d="M 530,0 L 170,0 170,1556 530,1556 530,0 Z"/>
   <glyph unicode="i" horiz-adv-x="398" d="M 530,0 L 170,0 170,1123 530,1123 530,0 Z M 540,1283 L 160,1283 160,1556 540,1556 540,1283 Z"/>
   <glyph unicode="h" horiz-adv-x="1138" d="M 1298,0 L 936,0 936,557 C 936,602 934,648 929,693 924,738 916,771 905,792 892,817 872,835 847,846 821,857 785,863 739,863 706,863 673,858 640,847 606,836 569,819 530,796 L 530,0 170,0 170,1556 530,1556 530,999 C 594,1049 656,1087 715,1114 774,1141 839,1154 911,1154 1032,1154 1127,1119 1196,1048 1264,977 1298,872 1298,731 L 1298,0 Z"/>
   <glyph unicode="g" horiz-adv-x="1192" d="M 1262,129 C 1262,24 1247,-65 1217,-137 1187,-209 1145,-265 1091,-306 1037,-347 972,-377 896,-396 819,-414 733,-423 637,-423 559,-423 482,-418 407,-409 331,-400 265,-388 210,-375 L 210,-94 254,-94 C 298,-111 352,-127 415,-142 478,-156 535,-163 585,-163 652,-163 706,-157 748,-145 789,-132 821,-115 843,-92 864,-71 879,-43 888,-10 897,23 902,63 902,110 L 902,131 C 859,96 811,68 758,47 705,26 647,16 582,16 425,16 303,63 218,158 133,253 90,397 90,590 90,683 103,763 129,830 155,897 192,956 239,1006 283,1053 337,1089 402,1115 466,1141 532,1154 599,1154 660,1154 715,1147 765,1133 814,1118 859,1098 900,1073 L 913,1123 1262,1123 1262,129 Z M 902,354 L 902,863 C 881,872 856,879 826,884 796,889 769,891 745,891 650,891 579,864 532,810 485,755 461,679 461,582 461,474 481,399 522,356 562,313 622,292 701,292 737,292 772,298 807,309 842,320 873,335 902,354 Z"/>
   <glyph unicode="e" horiz-adv-x="1192" d="M 1276,495 L 452,495 C 457,407 491,340 553,293 614,246 705,223 825,223 901,223 975,237 1046,264 1117,291 1174,321 1215,352 L 1255,352 1255,63 C 1174,30 1097,7 1025,-8 953,-23 873,-30 786,-30 561,-30 388,21 268,122 148,223 88,368 88,555 88,740 145,887 259,996 372,1104 528,1158 726,1158 909,1158 1046,1112 1138,1020 1230,927 1276,794 1276,621 L 1276,495 Z M 918,706 C 916,781 897,838 862,876 827,914 772,933 697,933 628,933 571,915 526,879 481,843 456,785 451,706 L 918,706 Z"/>
   <glyph unicode="c" horiz-adv-x="1060" d="M 755,-30 C 657,-30 568,-18 487,5 406,28 335,64 275,113 216,162 170,223 137,297 104,371 88,458 88,557 88,662 106,752 141,828 176,904 224,967 287,1017 348,1064 418,1099 497,1121 576,1143 659,1154 744,1154 821,1154 891,1146 956,1129 1021,1112 1081,1091 1137,1064 L 1137,757 1086,757 C 1072,769 1055,783 1036,799 1016,815 992,831 963,846 936,861 906,873 873,883 840,892 802,897 759,897 663,897 589,867 538,806 486,745 460,662 460,557 460,449 487,367 540,311 593,255 668,227 765,227 810,227 851,232 888,243 924,253 954,265 978,279 1001,292 1021,306 1038,321 1055,336 1071,350 1086,364 L 1137,364 1137,57 C 1080,30 1021,9 960,-7 898,-22 830,-30 755,-30 Z"/>
   <glyph unicode="a" horiz-adv-x="1139" d="M 850,293 L 850,527 C 801,523 749,518 692,511 635,504 592,495 563,486 527,475 500,458 481,437 462,415 452,386 452,351 452,328 454,309 458,294 462,279 472,265 488,252 503,239 522,229 543,223 564,216 598,213 643,213 679,213 716,220 753,235 790,250 822,269 850,293 Z M 850,119 C 831,104 807,87 778,66 749,45 722,29 697,17 662,1 625,-11 587,-19 549,-26 507,-30 462,-30 355,-30 266,3 194,70 122,136 86,221 86,324 86,406 104,474 141,526 178,578 230,619 297,650 364,680 446,701 545,714 644,727 746,733 852,733 L 852,739 C 852,803 827,847 776,871 725,896 651,908 552,908 493,908 429,898 362,877 295,856 246,839 217,828 L 184,828 184,1099 C 222,1109 284,1121 370,1135 455,1148 541,1155 627,1155 832,1155 980,1124 1071,1061 1162,998 1207,899 1207,764 L 1207,0 850,0 850,119 Z"/>
   <glyph unicode="T" horiz-adv-x="1324" d="M 1355,1201 L 890,1201 890,0 506,0 506,1201 41,1201 41,1489 1355,1489 1355,1201 Z"/>
   <glyph unicode="P" horiz-adv-x="1245" d="M 1419,1019 C 1419,952 1407,887 1384,824 1361,760 1327,706 1284,663 1225,604 1159,560 1086,530 1013,500 922,485 813,485 L 574,485 574,0 190,0 190,1489 822,1489 C 917,1489 997,1481 1062,1465 1127,1448 1184,1424 1234,1391 1294,1352 1340,1301 1372,1240 1403,1179 1419,1105 1419,1019 Z M 1022,1011 C 1022,1053 1011,1090 988,1120 965,1151 939,1172 909,1184 869,1200 830,1208 792,1208 754,1208 703,1208 640,1208 L 574,1208 574,765 684,765 C 749,765 803,769 846,777 888,785 923,801 952,825 977,847 995,872 1006,902 1017,932 1022,968 1022,1011 Z"/>
   <glyph unicode="N" horiz-adv-x="1377" d="M 1544,0 L 1174,0 542,1022 542,0 190,0 190,1489 649,1489 1192,636 1192,1489 1544,1489 1544,0 Z"/>
   <glyph unicode="M" horiz-adv-x="1562" d="M 1751,0 L 1369,0 1369,997 1093,350 828,350 552,997 552,0 190,0 190,1489 636,1489 971,742 1305,1489 1751,1489 1751,0 Z"/>
   <glyph unicode="F" horiz-adv-x="1086" d="M 1257,1201 L 572,1201 572,924 1207,924 1207,636 572,636 572,0 190,0 190,1489 1257,1489 1257,1201 Z"/>
   <glyph unicode="E" horiz-adv-x="1086" d="M 1267,0 L 190,0 190,1489 1267,1489 1267,1201 572,1201 572,944 1217,944 1217,656 572,656 572,288 1267,288 1267,0 Z"/>
   <glyph unicode="C" horiz-adv-x="1324" d="M 863,-29 C 752,-29 650,-13 557,20 463,53 382,101 315,166 248,231 196,311 159,408 122,505 103,616 103,743 103,861 121,968 156,1064 191,1160 243,1242 310,1311 375,1377 455,1428 551,1464 646,1500 751,1518 864,1518 927,1518 983,1515 1034,1508 1084,1501 1130,1491 1173,1480 1218,1467 1258,1453 1295,1438 1331,1422 1363,1407 1390,1394 L 1390,1033 1346,1033 C 1327,1049 1304,1068 1276,1090 1247,1112 1215,1134 1179,1155 1142,1176 1103,1194 1060,1209 1017,1224 972,1231 923,1231 869,1231 818,1223 769,1206 720,1189 675,1160 634,1121 595,1083 563,1033 539,970 514,907 502,831 502,742 502,649 515,571 542,508 568,445 601,396 641,360 682,323 727,297 777,282 827,266 876,258 925,258 972,258 1018,265 1064,279 1109,293 1151,312 1190,336 1223,355 1253,376 1281,398 1309,420 1332,439 1350,455 L 1390,455 1390,99 C 1353,82 1317,67 1283,52 1249,37 1213,25 1176,14 1127,0 1082,-11 1039,-18 996,-25 938,-29 863,-29 Z"/>
  </font>
 </defs>
 <defs class="TextShapeIndex">
  <g ooo:slide="id1" ooo:id-list="id3 id4 id5 id6 id7 id8 id9"/>
 </defs>
 <defs class="EmbeddedBulletChars">
  <g id="bullet-char-template(57356)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 580,1141 L 1163,571 580,0 -4,571 580,1141 Z"/>
  </g>
  <g id="bullet-char-template(57354)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 8,1128 L 1137,1128 1137,0 8,0 8,1128 Z"/>
  </g>
  <g id="bullet-char-template(10146)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 174,0 L 602,739 174,1481 1456,739 174,0 Z M 1358,739 L 309,1346 659,739 1358,739 Z"/>
  </g>
  <g id="bullet-char-template(10132)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 2015,739 L 1276,0 717,0 1260,543 174,543 174,936 1260,936 717,1481 1274,1481 2015,739 Z"/>
  </g>
  <g id="bullet-char-template(10007)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 0,-2 C -7,14 -16,27 -25,37 L 356,567 C 262,823 215,952 215,954 215,979 228,992 255,992 264,992 276,990 289,987 310,991 331,999 354,1012 L 381,999 492,748 772,1049 836,1024 860,1049 C 881,1039 901,1025 922,1006 886,937 835,863 770,784 769,783 710,716 594,584 L 774,223 C 774,196 753,168 711,139 L 727,119 C 717,90 699,76 672,76 641,76 570,178 457,381 L 164,-76 C 142,-110 111,-127 72,-127 30,-127 9,-110 8,-76 1,-67 -2,-52 -2,-32 -2,-23 -1,-13 0,-2 Z"/>
  </g>
  <g id="bullet-char-template(10004)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 285,-33 C 182,-33 111,30 74,156 52,228 41,333 41,471 41,549 55,616 82,672 116,743 169,778 240,778 293,778 328,747 346,684 L 369,508 C 377,444 397,411 428,410 L 1163,1116 C 1174,1127 1196,1133 1229,1133 1271,1133 1292,1118 1292,1087 L 1292,965 C 1292,929 1282,901 1262,881 L 442,47 C 390,-6 338,-33 285,-33 Z"/>
  </g>
  <g id="bullet-char-template(9679)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 813,0 C 632,0 489,54 383,161 276,268 223,411 223,592 223,773 276,916 383,1023 489,1130 632,1184 813,1184 992,1184 1136,1130 1245,1023 1353,916 1407,772 1407,592 1407,412 1353,268 1245,161 1136,54 992,0 813,0 Z"/>
  </g>
  <g id="bullet-char-template(8226)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 346,457 C 273,457 209,483 155,535 101,586 74,649 74,723 74,796 101,859 155,911 209,963 273,989 346,989 419,989 480,963 531,910 582,859 608,796 608,723 608,648 583,586 532,535 482,483 420,457 346,457 Z"/>
  </g>
  <g id="bullet-char-template(8211)" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M -4,459 L 1135,459 1135,606 -4,606 -4,459 Z"/>
  </g>
 </defs>
 <defs class="TextEmbeddedBitmaps"/>
 <g>
  <g id="id2" class="Master_Slide">
   <g id="bg-id2" class="Background"/>
   <g id="bo-id2" class="BackgroundObjects"/>
  </g>
 </g>
 <g class="SlideGroup">
  <g>
   <g id="id1" class="Slide" clip-path="url(#presentation_clip_path)">
    <g class="Page">
     <g class="com.sun.star.drawing.CustomShape">
      <g id="id3">
       <rect class="BoundingBox" stroke="none" fill="none" x="4528" y="1533" width="4776" height="2363"/>
       <path fill="rgb(128,128,128)" stroke="none" d="M 5089,1734 C 4909,1734 4729,1914 4729,2094 L 4729,3534 C 4729,3714 4909,3894 5089,3894 L 8942,3894 C 9122,3894 9302,3714 9302,3534 L 9302,2094 C 9302,1914 9122,1734 8942,1734 L 5089,1734 Z M 4729,1734 L 4729,1734 Z M 9302,3894 L 9302,3894 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 5089,1734 C 4909,1734 4729,1914 4729,2094 L 4729,3534 C 4729,3714 4909,3894 5089,3894 L 8942,3894 C 9122,3894 9302,3714 9302,3534 L 9302,2094 C 9302,1914 9122,1734 8942,1734 L 5089,1734 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 4729,1734 L 4729,1734 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 9302,3894 L 9302,3894 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="5186" y="2983"><tspan fill="rgb(128,128,128)" stroke="none">ExecuteMission</tspan></tspan></tspan></text>
       <path fill="rgb(0,102,204)" stroke="none" d="M 4889,1534 C 4709,1534 4529,1714 4529,1894 L 4529,3334 C 4529,3514 4709,3694 4889,3694 L 8742,3694 C 8922,3694 9102,3514 9102,3334 L 9102,1894 C 9102,1714 8922,1534 8742,1534 L 4889,1534 Z M 4529,1534 L 4529,1534 Z M 9102,3694 L 9102,3694 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 4889,1534 C 4709,1534 4529,1714 4529,1894 L 4529,3334 C 4529,3514 4709,3694 4889,3694 L 8742,3694 C 8922,3694 9102,3514 9102,3334 L 9102,1894 C 9102,1714 8922,1534 8742,1534 L 4889,1534 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 4529,1534 L 4529,1534 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 9102,3694 L 9102,3694 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="4986" y="2783"><tspan fill="rgb(255,255,255)" stroke="none">ExecuteMission</tspan></tspan></tspan></text>
      </g>
     </g>
     <g class="com.sun.star.drawing.CustomShape">
      <g id="id4">
       <rect class="BoundingBox" stroke="none" fill="none" x="4655" y="5470" width="4776" height="2363"/>
       <path fill="rgb(128,128,128)" stroke="none" d="M 5216,5671 C 5036,5671 4856,5851 4856,6031 L 4856,7471 C 4856,7651 5036,7831 5216,7831 L 9069,7831 C 9249,7831 9429,7651 9429,7471 L 9429,6031 C 9429,5851 9249,5671 9069,5671 L 5216,5671 Z M 4856,5671 L 4856,5671 Z M 9429,7831 L 9429,7831 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 5216,5671 C 5036,5671 4856,5851 4856,6031 L 4856,7471 C 4856,7651 5036,7831 5216,7831 L 9069,7831 C 9249,7831 9429,7651 9429,7471 L 9429,6031 C 9429,5851 9249,5671 9069,5671 L 5216,5671 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 4856,5671 L 4856,5671 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 9429,7831 L 9429,7831 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="5229" y="6920"><tspan fill="rgb(128,128,128)" stroke="none">NavigateToPose</tspan></tspan></tspan></text>
       <path fill="rgb(0,102,204)" stroke="none" d="M 5016,5471 C 4836,5471 4656,5651 4656,5831 L 4656,7271 C 4656,7451 4836,7631 5016,7631 L 8869,7631 C 9049,7631 9229,7451 9229,7271 L 9229,5831 C 9229,5651 9049,5471 8869,5471 L 5016,5471 Z M 4656,5471 L 4656,5471 Z M 9229,7631 L 9229,7631 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 5016,5471 C 4836,5471 4656,5651 4656,5831 L 4656,7271 C 4656,7451 4836,7631 5016,7631 L 8869,7631 C 9049,7631 9229,7451 9229,7271 L 9229,5831 C 9229,5651 9049,5471 8869,5471 L 5016,5471 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 4656,5471 L 4656,5471 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 9229,7631 L 9229,7631 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="5029" y="6720"><tspan fill="rgb(255,255,255)" stroke="none">NavigateToPose</tspan></tspan></tspan></text>
      </g>
     </g>
     <g class="com.sun.star.drawing.CustomShape">
      <g id="id5">
       <rect class="BoundingBox" stroke="none" fill="none" x="1352" y="9788" width="5665" height="2363"/>
       <path fill="rgb(128,128,128)" stroke="none" d="M 1912,9989 C 1732,9989 1553,10169 1553,10349 L 1553,11789 C 1553,11969 1732,12149 1912,12149 L 6655,12149 C 6835,12149 7015,11969 7015,11789 L 7015,10349 C 7015,10169 6835,9989 6655,9989 L 1912,9989 Z M 1553,9989 L 1553,9989 Z M 7015,12149 L 7015,12149 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 1912,9989 C 1732,9989 1553,10169 1553,10349 L 1553,11789 C 1553,11969 1732,12149 1912,12149 L 6655,12149 C 6835,12149 7015,11969 7015,11789 L 7015,10349 C 7015,10169 6835,9989 6655,9989 L 1912,9989 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 1553,9989 L 1553,9989 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 7015,12149 L 7015,12149 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="1826" y="11238"><tspan fill="rgb(128,128,128)" stroke="none">ComputePathToPose</tspan></tspan></tspan></text>
       <path fill="rgb(0,102,204)" stroke="none" d="M 1712,9789 C 1532,9789 1353,9969 1353,10149 L 1353,11589 C 1353,11769 1532,11949 1712,11949 L 6455,11949 C 6635,11949 6815,11769 6815,11589 L 6815,10149 C 6815,9969 6635,9789 6455,9789 L 1712,9789 Z M 1353,9789 L 1353,9789 Z M 6815,11949 L 6815,11949 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 1712,9789 C 1532,9789 1353,9969 1353,10149 L 1353,11589 C 1353,11769 1532,11949 1712,11949 L 6455,11949 C 6635,11949 6815,11769 6815,11589 L 6815,10149 C 6815,9969 6635,9789 6455,9789 L 1712,9789 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 1353,9789 L 1353,9789 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 6815,11949 L 6815,11949 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="1626" y="11038"><tspan fill="rgb(255,255,255)" stroke="none">ComputePathToPose</tspan></tspan></tspan></text>
      </g>
     </g>
     <g class="com.sun.star.drawing.CustomShape">
      <g id="id6">
       <rect class="BoundingBox" stroke="none" fill="none" x="7703" y="9788" width="4776" height="2363"/>
       <path fill="rgb(128,128,128)" stroke="none" d="M 8264,9989 C 8084,9989 7904,10169 7904,10349 L 7904,11789 C 7904,11969 8084,12149 8264,12149 L 12117,12149 C 12297,12149 12477,11969 12477,11789 L 12477,10349 C 12477,10169 12297,9989 12117,9989 L 8264,9989 Z M 7904,9989 L 7904,9989 Z M 12477,12149 L 12477,12149 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 8264,9989 C 8084,9989 7904,10169 7904,10349 L 7904,11789 C 7904,11969 8084,12149 8264,12149 L 12117,12149 C 12297,12149 12477,11969 12477,11789 L 12477,10349 C 12477,10169 12297,9989 12117,9989 L 8264,9989 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 7904,9989 L 7904,9989 Z"/>
       <path fill="none" stroke="rgb(128,128,128)" d="M 12477,12149 L 12477,12149 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="8865" y="11238"><tspan fill="rgb(128,128,128)" stroke="none">FollowPath</tspan></tspan></tspan></text>
       <path fill="rgb(0,102,204)" stroke="none" d="M 8064,9789 C 7884,9789 7704,9969 7704,10149 L 7704,11589 C 7704,11769 7884,11949 8064,11949 L 11917,11949 C 12097,11949 12277,11769 12277,11589 L 12277,10149 C 12277,9969 12097,9789 11917,9789 L 8064,9789 Z M 7704,9789 L 7704,9789 Z M 12277,11949 L 12277,11949 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 8064,9789 C 7884,9789 7704,9969 7704,10149 L 7704,11589 C 7704,11769 7884,11949 8064,11949 L 11917,11949 C 12097,11949 12277,11769 12277,11589 L 12277,10149 C 12277,9969 12097,9789 11917,9789 L 8064,9789 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 7704,9789 L 7704,9789 Z"/>
       <path fill="none" stroke="rgb(52,101,164)" d="M 12277,11949 L 12277,11949 Z"/>
       <text class="TextShape"><tspan class="TextParagraph" font-family="Verdana, sans-serif" font-size="423px" font-weight="700"><tspan class="TextPosition" x="8665" y="11038"><tspan fill="rgb(255,255,255)" stroke="none">FollowPath</tspan></tspan></tspan></text>
      </g>
     </g>
     <g class="com.sun.star.drawing.LineShape">
      <g id="id7">
       <rect class="BoundingBox" stroke="none" fill="none" x="6792" y="3693" width="301" height="1780"/>
       <path fill="none" stroke="rgb(0,0,0)" d="M 6942,3694 L 6942,5042"/>
       <path fill="rgb(0,0,0)" stroke="none" d="M 6942,5472 L 7092,5022 6792,5022 6942,5472 Z"/>
      </g>
     </g>
     <g class="com.sun.star.drawing.LineShape">
      <g id="id8">
       <rect class="BoundingBox" stroke="none" fill="none" x="6814" y="7630" width="3050" height="2161"/>
       <path fill="none" stroke="rgb(0,0,0)" d="M 6815,7631 L 9512,9541"/>
       <path fill="rgb(0,0,0)" stroke="none" d="M 9863,9790 L 9582,9407 9409,9652 9863,9790 Z"/>
      </g>
     </g>
     <g class="com.sun.star.drawing.LineShape">
      <g id="id9">
       <rect class="BoundingBox" stroke="none" fill="none" x="4021" y="7630" width="2796" height="2161"/>
       <path fill="none" stroke="rgb(0,0,0)" d="M 6815,7631 L 4361,9527"/>
       <path fill="rgb(0,0,0)" stroke="none" d="M 4021,9790 L 4469,9634 4285,9396 4021,9790 Z"/>
      </g>
     </g>
    </g>
   </g>
  </g>
 </g>
</svg>
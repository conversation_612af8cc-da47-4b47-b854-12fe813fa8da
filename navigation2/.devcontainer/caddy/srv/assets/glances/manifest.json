{
    "name": "Glances: {{placeholder "http.vars.ReqHost"}}",
    "short_name": "Glances: {{placeholder "http.vars.ReqHost"}}",
    "icons": [
        {
          "src": "/media/icons/glances/any_icon_x512.webp",
          "sizes": "512x512",
          "type": "image/webp",
          "purpose": "any"
        },
        {
          "src": "/media/icons/glances/maskable_icon_x512.webp",
          "sizes": "512x512",
          "type": "image/webp",
          "purpose": "maskable"
        }
    ],
    "id": "/glances/",
    "start_url": "/glances/",
    "theme_color": "#2C363F",
    "background_color": "#2C363F",
    "display": "fullscreen",
    "shortcuts" : [
        {
          "name": "Refresh 1sec",
          "url": "/glances/1",
          "description": "Refresh page every 1 second"
        },
        {
          "name": "Refresh 5sec",
          "url": "/glances/5",
          "description": "Refresh page every 5 seconds"
        },
        {
          "name": "Refresh 10sec",
          "url": "/glances/10",
          "description": "Refresh page every 10 seconds"
        }
      ]
}

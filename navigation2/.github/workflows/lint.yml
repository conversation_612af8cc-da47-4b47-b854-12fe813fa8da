name: Lint
on:
  pull_request:

jobs:
  ament_lint_general:
    name: ament_${{ matrix.linter }}
    runs-on: ubuntu-latest
    container:
      image: rostooling/setup-ros-docker:ubuntu-noble-ros-jazzy-ros-base-latest
    strategy:
      fail-fast: false
      matrix:
          linter: [xmllint, cpplint, uncrustify, pep257, flake8]
    steps:
    - uses: actions/checkout@v4
    - uses: ros-tooling/action-ros-lint@v0.1
      with:
        linter: ${{ matrix.linter }}
        distribution: jazzy
        package-name: "*"

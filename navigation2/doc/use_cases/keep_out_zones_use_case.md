# Keep Out Zones
As a Robot user I want to be able to designate keep-out zones or areas on a map so that my robot will go around those areas instead of through them 

## More details
- Why is this needed?
   - This is needed for indoor/outdoor robot navigation in areas where there may be safety issues or hazards
   - Example: a courier robot in a logistics warehouse may be required to avoid areas where the forklift is unloading pallets from trucks

- What is the expected user interaction?
   - The user should be able to specify keep out zones for the robot to avoid

- Are there any non-functional requirements? (build system, tools, performance, etc)



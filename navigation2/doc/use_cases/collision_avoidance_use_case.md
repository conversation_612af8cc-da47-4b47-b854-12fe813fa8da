# Collision Avoidance
As a Robot user I want to my robot to avoid colliding with people or objects so that it won't damage anything or hurt anyone

## More details
- Why is this needed?
   - This is needed for indoor and outdoor robot navigation in most (all?) cases
   - Example: a robot in a warehouse should avoid colliding into the walls or shelving, and dynamically avoid people that cross its path

- What is the expected user interaction?
   - The user should be able to walk in front of a robot and it should avoid crashing into that person

- Are there any non-functional requirements? (build system, tools, performance, etc)



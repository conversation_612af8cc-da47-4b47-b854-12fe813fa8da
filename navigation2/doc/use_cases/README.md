

# Target Use Cases
The Nav2 system is targeting the following use cases:

[2D Indoor Navigation](indoor_navigation_use_case.md) - example: Warehouse / Logistics robot

2D Navigation with Elevation - example: rescue robot navigating building with ramps and stairways

2D Navigation with Elevators - example: Room service robot

[Outdoor Navigation](outdoor_navigation_use_case.md)

## Stretch Target
3D Navigation - Drones


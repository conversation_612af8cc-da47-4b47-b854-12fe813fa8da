# Outdoor Navigation
As a Robot user I want my robot to autonomously navigate to a given location on a given outdoor map, such as a college campus or street, so that it can do something useful at that location

## More details
- Why is this needed?
   - This is needed for outdoor robot navigation in most (all?) cases
   - Example: a delivery robot on a college campus

- What is the expected user interaction?
   - The user should be able to specify a map to use and a location on that map for the robot to move to. 

- Are there any non-functional requirements? (build system, tools, performance, etc)


